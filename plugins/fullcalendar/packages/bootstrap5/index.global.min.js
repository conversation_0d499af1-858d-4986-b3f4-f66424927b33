/*!
FullCalendar Bootstrap 5 Plugin v6.1.19
Docs & License: https://fullcalendar.io/docs/bootstrap5
(c) 2024 Adam <PERSON>
*/
FullCalendar.Bootstrap5=function(e,t,o){"use strict";class r extends o.Theme{}r.prototype.classes={root:"fc-theme-bootstrap5",tableCellShaded:"fc-theme-bootstrap5-shaded",buttonGroup:"btn-group",button:"btn btn-primary",buttonActive:"active",popover:"popover",popoverHeader:"popover-header",popoverContent:"popover-body"},r.prototype.baseIconClass="bi",r.prototype.iconClasses={close:"bi-x-lg",prev:"bi-chevron-left",next:"bi-chevron-right",prevYear:"bi-chevron-double-left",nextYear:"bi-chevron-double-right"},r.prototype.rtlIconClasses={prev:"bi-chevron-right",next:"bi-chevron-left",prevYear:"bi-chevron-double-right",nextYear:"bi-chevron-double-left"},r.prototype.iconOverrideOption="buttonIcons",r.prototype.iconOverrideCustomButtonOption="icon",r.prototype.iconOverridePrefix="bi-";o.injectStyles(".fc-theme-bootstrap5 a:not([href]){color:inherit;text-decoration:inherit}.fc-theme-bootstrap5 .fc-list,.fc-theme-bootstrap5 .fc-scrollgrid,.fc-theme-bootstrap5 td,.fc-theme-bootstrap5 th{border:1px solid var(--bs-gray-400)}.fc-theme-bootstrap5 .fc-scrollgrid{border-bottom-width:0;border-right-width:0}.fc-theme-bootstrap5-shaded{background-color:var(--bs-gray-200)}");var a=t.createPlugin({name:"@fullcalendar/bootstrap5",themeClasses:{bootstrap5:r}}),n={__proto__:null,BootstrapTheme:r};return t.globalPlugins.push(a),e.Internal=n,e.default=a,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar,FullCalendar.Internal);