/*!
FullCalendar Time Grid Plugin v6.1.19
Docs & License: https://fullcalendar.io/docs/timegrid-view
(c) 2024 Adam Shaw
*/
FullCalendar.TimeGrid=function(e,t,i,r,n){"use strict";class o extends i.Splitter{getKeyInfo(){return{allDay:{},timed:{}}}getKeysForDateSpan(e){return e.allDay?["allDay"]:["timed"]}getKeysForEventDef(e){return e.allDay?i.hasBgRendering(e)?["timed","allDay"]:["allDay"]:["timed"]}}const s=i.createFormatter({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"short"});function l(e){let t=["fc-timegrid-slot","fc-timegrid-slot-label",e.isLabeled?"fc-scrollgrid-shrink":"fc-timegrid-slot-minor"];return r.createElement(i.ViewContextType.Consumer,null,n=>{if(!e.isLabeled)return r.createElement("td",{className:t.join(" "),"data-time":e.isoTimeStr});let{dateEnv:o,options:l,viewApi:c}=n,d=null==l.slotLabelFormat?s:Array.isArray(l.slotLabelFormat)?i.createFormatter(l.slotLabelFormat[0]):i.createFormatter(l.slotLabelFormat),m={level:0,time:e.time,date:o.toDate(e.date),view:c,text:o.format(e.date,d)};return r.createElement(i.ContentContainer,{elTag:"td",elClasses:t,elAttrs:{"data-time":e.isoTimeStr},renderProps:m,generatorName:"slotLabelContent",customGenerator:l.slotLabelContent,defaultGenerator:a,classNameGenerator:l.slotLabelClassNames,didMount:l.slotLabelDidMount,willUnmount:l.slotLabelWillUnmount},e=>r.createElement("div",{className:"fc-timegrid-slot-label-frame fc-scrollgrid-shrink-frame"},r.createElement(e,{elTag:"div",elClasses:["fc-timegrid-slot-label-cushion","fc-scrollgrid-shrink-cushion"]})))})}function a(e){return e.text}class c extends i.BaseComponent{render(){return this.props.slatMetas.map(e=>r.createElement("tr",{key:e.key},r.createElement(l,Object.assign({},e))))}}const d=i.createFormatter({week:"short"});class m extends i.DateComponent{constructor(){super(...arguments),this.allDaySplitter=new o,this.headerElRef=r.createRef(),this.rootElRef=r.createRef(),this.scrollerElRef=r.createRef(),this.state={slatCoords:null},this.handleScrollTopRequest=e=>{let t=this.scrollerElRef.current;t&&(t.scrollTop=e)},this.renderHeadAxis=(e,t="")=>{let{options:n}=this.context,{dateProfile:o}=this.props,s=o.renderRange,l=1===i.diffDays(s.start,s.end)?i.buildNavLinkAttrs(this.context,s.start,"week"):{};return n.weekNumbers&&"day"===e?r.createElement(i.WeekNumberContainer,{elTag:"th",elClasses:["fc-timegrid-axis","fc-scrollgrid-shrink"],elAttrs:{"aria-hidden":!0},date:s.start,defaultFormat:d},e=>r.createElement("div",{className:["fc-timegrid-axis-frame","fc-scrollgrid-shrink-frame","fc-timegrid-axis-frame-liquid"].join(" "),style:{height:t}},r.createElement(e,{elTag:"a",elClasses:["fc-timegrid-axis-cushion","fc-scrollgrid-shrink-cushion","fc-scrollgrid-sync-inner"],elAttrs:l}))):r.createElement("th",{"aria-hidden":!0,className:"fc-timegrid-axis"},r.createElement("div",{className:"fc-timegrid-axis-frame",style:{height:t}}))},this.renderTableRowAxis=e=>{let{options:t,viewApi:n}=this.context,o={text:t.allDayText,view:n};return r.createElement(i.ContentContainer,{elTag:"td",elClasses:["fc-timegrid-axis","fc-scrollgrid-shrink"],elAttrs:{"aria-hidden":!0},renderProps:o,generatorName:"allDayContent",customGenerator:t.allDayContent,defaultGenerator:f,classNameGenerator:t.allDayClassNames,didMount:t.allDayDidMount,willUnmount:t.allDayWillUnmount},t=>r.createElement("div",{className:["fc-timegrid-axis-frame","fc-scrollgrid-shrink-frame",null==e?" fc-timegrid-axis-frame-liquid":""].join(" "),style:{height:e}},r.createElement(t,{elTag:"span",elClasses:["fc-timegrid-axis-cushion","fc-scrollgrid-shrink-cushion","fc-scrollgrid-sync-inner"]})))},this.handleSlatCoords=e=>{this.setState({slatCoords:e})}}renderSimpleLayout(e,t,n){let{context:o,props:s}=this,l=[],a=i.getStickyHeaderDates(o.options);return e&&l.push({type:"header",key:"header",isSticky:a,chunk:{elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}}),t&&(l.push({type:"body",key:"all-day",chunk:{content:t}}),l.push({type:"body",key:"all-day-divider",outerContent:r.createElement("tr",{role:"presentation",className:"fc-scrollgrid-section"},r.createElement("td",{className:"fc-timegrid-divider "+o.theme.getClass("tableCellShaded")}))})),l.push({type:"body",key:"body",liquid:!0,expandRows:Boolean(o.options.expandRows),chunk:{scrollerElRef:this.scrollerElRef,content:n}}),r.createElement(i.ViewContainer,{elRef:this.rootElRef,elClasses:["fc-timegrid"],viewSpec:o.viewSpec},r.createElement(i.SimpleScrollGrid,{liquid:!s.isHeightAuto&&!s.forPrint,collapsibleWidth:s.forPrint,cols:[{width:"shrink"}],sections:l}))}renderHScrollLayout(e,t,n,o,s,l,a){let d=this.context.pluginHooks.scrollGridImpl;if(!d)throw new Error("No ScrollGrid implementation");let{context:m,props:f}=this,h=!f.forPrint&&i.getStickyHeaderDates(m.options),g=!f.forPrint&&i.getStickyFooterScrollbar(m.options),u=[];e&&u.push({type:"header",key:"header",isSticky:h,syncRowHeights:!0,chunks:[{key:"axis",rowContent:e=>r.createElement("tr",{role:"presentation"},this.renderHeadAxis("day",e.rowSyncHeights[0]))},{key:"cols",elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}]}),t&&(u.push({type:"body",key:"all-day",syncRowHeights:!0,chunks:[{key:"axis",rowContent:e=>r.createElement("tr",{role:"presentation"},this.renderTableRowAxis(e.rowSyncHeights[0]))},{key:"cols",content:t}]}),u.push({key:"all-day-divider",type:"body",outerContent:r.createElement("tr",{role:"presentation",className:"fc-scrollgrid-section"},r.createElement("td",{colSpan:2,className:"fc-timegrid-divider "+m.theme.getClass("tableCellShaded")}))}));let p=m.options.nowIndicator;return u.push({type:"body",key:"body",liquid:!0,expandRows:Boolean(m.options.expandRows),chunks:[{key:"axis",content:e=>r.createElement("div",{className:"fc-timegrid-axis-chunk"},r.createElement("table",{"aria-hidden":!0,style:{height:e.expandRows?e.clientHeight:""}},e.tableColGroupNode,r.createElement("tbody",null,r.createElement(c,{slatMetas:l}))),r.createElement("div",{className:"fc-timegrid-now-indicator-container"},r.createElement(i.NowTimer,{unit:p?"minute":"day"},e=>{let t=p&&a&&a.safeComputeTop(e);return"number"==typeof t?r.createElement(i.NowIndicatorContainer,{elClasses:["fc-timegrid-now-indicator-arrow"],elStyle:{top:t},isAxis:!0,date:e}):null})))},{key:"cols",scrollerElRef:this.scrollerElRef,content:n}]}),g&&u.push({key:"footer",type:"footer",isSticky:!0,chunks:[{key:"axis",content:i.renderScrollShim},{key:"cols",content:i.renderScrollShim}]}),r.createElement(i.ViewContainer,{elRef:this.rootElRef,elClasses:["fc-timegrid"],viewSpec:m.viewSpec},r.createElement(d,{liquid:!f.isHeightAuto&&!f.forPrint,forPrint:f.forPrint,collapsibleWidth:!1,colGroups:[{width:"shrink",cols:[{width:"shrink"}]},{cols:[{span:o,minWidth:s}]}],sections:u}))}getAllDayMaxEventProps(){let{dayMaxEvents:e,dayMaxEventRows:t}=this.context.options;return!0!==e&&!0!==t||(e=void 0,t=5),{dayMaxEvents:e,dayMaxEventRows:t}}}function f(e){return e.text}class h{constructor(e,t,i){this.positions=e,this.dateProfile=t,this.slotDuration=i}safeComputeTop(e){let{dateProfile:t}=this;if(i.rangeContainsMarker(t.currentRange,e)){let r=i.startOfDay(e),n=e.valueOf()-r.valueOf();if(n>=i.asRoughMs(t.slotMinTime)&&n<i.asRoughMs(t.slotMaxTime))return this.computeTimeTop(i.createDuration(n))}return null}computeDateTop(e,t){return t||(t=i.startOfDay(e)),this.computeTimeTop(i.createDuration(e.valueOf()-t.valueOf()))}computeTimeTop(e){let t,r,{positions:n,dateProfile:o}=this,s=n.els.length,l=(e.milliseconds-i.asRoughMs(o.slotMinTime))/i.asRoughMs(this.slotDuration);return l=Math.max(0,l),l=Math.min(s,l),t=Math.floor(l),t=Math.min(t,s-1),r=l-t,n.tops[t]+n.getHeight(t)*r}}class g extends i.BaseComponent{render(){let{props:e,context:t}=this,{options:n}=t,{slatElRefs:o}=e;return r.createElement("tbody",null,e.slatMetas.map((s,a)=>{let c={time:s.time,date:t.dateEnv.toDate(s.date),view:t.viewApi};return r.createElement("tr",{key:s.key,ref:o.createRef(s.key)},e.axis&&r.createElement(l,Object.assign({},s)),r.createElement(i.ContentContainer,{elTag:"td",elClasses:["fc-timegrid-slot","fc-timegrid-slot-lane",!s.isLabeled&&"fc-timegrid-slot-minor"],elAttrs:{"data-time":s.isoTimeStr},renderProps:c,generatorName:"slotLaneContent",customGenerator:n.slotLaneContent,classNameGenerator:n.slotLaneClassNames,didMount:n.slotLaneDidMount,willUnmount:n.slotLaneWillUnmount}))}))}}class u extends i.BaseComponent{constructor(){super(...arguments),this.rootElRef=r.createRef(),this.slatElRefs=new i.RefMap}render(){let{props:e,context:t}=this;return r.createElement("div",{ref:this.rootElRef,className:"fc-timegrid-slots"},r.createElement("table",{"aria-hidden":!0,className:t.theme.getClass("table"),style:{minWidth:e.tableMinWidth,width:e.clientWidth,height:e.minHeight}},e.tableColGroupNode,r.createElement(g,{slatElRefs:this.slatElRefs,axis:e.axis,slatMetas:e.slatMetas})))}componentDidMount(){this.updateSizing()}componentDidUpdate(){this.updateSizing()}componentWillUnmount(){this.props.onCoords&&this.props.onCoords(null)}updateSizing(){let{context:e,props:t}=this;if(t.onCoords&&null!==t.clientWidth){this.rootElRef.current.offsetHeight&&t.onCoords(new h(new i.PositionCache(this.rootElRef.current,(r=this.slatElRefs.currentMap,t.slatMetas.map(e=>r[e.key])),!1,!0),this.props.dateProfile,e.options.slotDuration))}var r}}function p(e,t){let i,r=[];for(i=0;i<t;i+=1)r.push([]);if(e)for(i=0;i<e.length;i+=1)r[e[i].col].push(e[i]);return r}function v(e,t){let i=[];if(e){for(let r=0;r<t;r+=1)i[r]={affectedInstances:e.affectedInstances,isEvent:e.isEvent,segs:[]};for(let t of e.segs)i[t.col].segs.push(t)}else for(let e=0;e<t;e+=1)i[e]=null;return i}class b extends i.BaseComponent{render(){let{props:e}=this;return r.createElement(i.MoreLinkContainer,{elClasses:["fc-timegrid-more-link"],elStyle:{top:e.top,bottom:e.bottom},allDayDate:null,moreCnt:e.hiddenSegs.length,allSegs:e.hiddenSegs,hiddenSegs:e.hiddenSegs,extraDateSpan:e.extraDateSpan,dateProfile:e.dateProfile,todayRange:e.todayRange,popoverContent:()=>T(e.hiddenSegs,e),defaultGenerator:y,forceTimed:!0},e=>r.createElement(e,{elTag:"div",elClasses:["fc-timegrid-more-link-inner","fc-sticky"]}))}}function y(e){return e.shortText}function x(e,t,r){let n=new i.SegHierarchy;null!=t&&(n.strictOrder=t),null!=r&&(n.maxStackCnt=r);let o=n.addSegs(e),s=i.groupIntersectingEntries(o),l=function(e){const{entriesByLevel:t}=e,r=w((e,t)=>e+":"+t,(n,o)=>{let s=S(function(e,t,r){let{levelCoords:n,entriesByLevel:o}=e,s=o[t][r],l=n[t]+s.thickness,a=n.length,c=t;for(;c<a&&n[c]<l;c+=1);for(;c<a;c+=1){let e,t=o[c],r=i.binarySearch(t,s.span.start,i.getEntrySpanEnd),n=r[0]+r[1],l=n;for(;(e=t[l])&&e.span.start<s.span.end;)l+=1;if(n<l)return{level:c,lateralStart:n,lateralEnd:l}}return null}(e,n,o),r),l=t[n][o];return[Object.assign(Object.assign({},l),{nextLevelNodes:s[0]}),l.thickness+s[1]]});return S(t.length?{level:0,lateralStart:0,lateralEnd:t[0].length}:null,r)[0]}(n);return l=function(e,t){const r=w((e,t,r)=>i.buildEntryKey(e),(e,i,n)=>{let o,{nextLevelNodes:s,thickness:l}=e,a=l+n,c=l/a,d=[];if(s.length)for(let e of s)if(void 0===o){let t=r(e,i,a);o=t[0],d.push(t[1])}else{let t=r(e,o,0);d.push(t[1])}else o=t;let m=(o-i)*c;return[o-m,Object.assign(Object.assign({},e),{thickness:m,nextLevelNodes:d})]});return e.map(e=>r(e,0,0)[1])}(l,1),{segRects:function(e){let t=[];const r=w((e,t,r)=>i.buildEntryKey(e),(e,i,r)=>{let o=Object.assign(Object.assign({},e),{levelCoord:i,stackDepth:r,stackForward:0});return t.push(o),o.stackForward=n(e.nextLevelNodes,i+e.thickness,r+1)+1});function n(e,t,i){let n=0;for(let o of e)n=Math.max(r(o,t,i),n);return n}return n(e,0,0),t}(l),hiddenGroups:s}}function S(e,t){if(!e)return[[],0];let{level:i,lateralStart:r,lateralEnd:n}=e,o=r,s=[];for(;o<n;)s.push(t(i,o)),o+=1;return s.sort(C),[s.map(E),s[0][1]]}function C(e,t){return t[1]-e[1]}function E(e){return e[0]}function w(e,t){const i={};return(...r)=>{let n=e(...r);return n in i?i[n]:i[n]=t(...r)}}function D(e,t,i=null,r=0){let n=[];if(i)for(let o=0;o<e.length;o+=1){let s=e[o],l=i.computeDateTop(s.start,t),a=Math.max(l+(r||0),i.computeDateTop(s.end,t));n.push({start:Math.round(l),end:Math.round(a)})}return n}const R=i.createFormatter({hour:"numeric",minute:"2-digit",meridiem:!1});class k extends i.BaseComponent{render(){return r.createElement(i.StandardEvent,Object.assign({},this.props,{elClasses:["fc-timegrid-event","fc-v-event",this.props.isShort&&"fc-timegrid-event-short"],defaultTimeFormat:R}))}}class M extends i.BaseComponent{constructor(){super(...arguments),this.sortEventSegs=i.memoize(i.sortEventSegs)}render(){let{props:e,context:t}=this,{options:n}=t,o=n.selectMirror,s=e.eventDrag&&e.eventDrag.segs||e.eventResize&&e.eventResize.segs||o&&e.dateSelectionSegs||[],l=e.eventDrag&&e.eventDrag.affectedInstances||e.eventResize&&e.eventResize.affectedInstances||{},a=this.sortEventSegs(e.fgEventSegs,n.eventOrder);return r.createElement(i.DayCellContainer,{elTag:"td",elRef:e.elRef,elClasses:["fc-timegrid-col",...e.extraClassNames||[]],elAttrs:Object.assign({role:"gridcell"},e.extraDataAttrs),date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,extraRenderProps:e.extraRenderProps},t=>r.createElement("div",{className:"fc-timegrid-col-frame"},r.createElement("div",{className:"fc-timegrid-col-bg"},this.renderFillSegs(e.businessHourSegs,"non-business"),this.renderFillSegs(e.bgEventSegs,"bg-event"),this.renderFillSegs(e.dateSelectionSegs,"highlight")),r.createElement("div",{className:"fc-timegrid-col-events"},this.renderFgSegs(a,l,!1,!1,!1)),r.createElement("div",{className:"fc-timegrid-col-events"},this.renderFgSegs(s,{},Boolean(e.eventDrag),Boolean(e.eventResize),Boolean(o),"mirror")),r.createElement("div",{className:"fc-timegrid-now-indicator-container"},this.renderNowIndicator(e.nowIndicatorSegs)),i.hasCustomDayCellContent(n)&&r.createElement(t,{elTag:"div",elClasses:["fc-timegrid-col-misc"]})))}renderFgSegs(e,t,i,r,n,o){let{props:s}=this;return s.forPrint?T(e,s):this.renderPositionedFgSegs(e,t,i,r,n,o)}renderPositionedFgSegs(e,t,n,o,s,l){let{eventMaxStack:a,eventShortHeight:c,eventOrderStrict:d,eventMinHeight:m}=this.context.options,{date:f,slatCoords:h,eventSelection:g,todayRange:u,nowDate:p}=this.props,v=n||o||s,b=D(e,f,h,m),{segPlacements:y,hiddenGroups:S}=function(e,t,i,r){let n=[],o=[];for(let i=0;i<e.length;i+=1){let r=t[i];r?n.push({index:i,thickness:1,span:r}):o.push(e[i])}let{segRects:s,hiddenGroups:l}=x(n,i,r),a=[];for(let t of s)a.push({seg:e[t.index],rect:t});for(let e of o)a.push({seg:e,rect:null});return{segPlacements:a,hiddenGroups:l}}(e,b,d,a);return r.createElement(r.Fragment,null,this.renderHiddenGroups(S,e),y.map(e=>{let{seg:a,rect:d}=e,m=a.eventRange.instance.instanceId,f=v||Boolean(!t[m]&&d),h=N(d&&d.span),b=!v&&d?this.computeSegHStyle(d):{left:0,right:0},y=Boolean(d)&&d.stackForward>0,x=Boolean(d)&&d.span.end-d.span.start<c;return r.createElement("div",{className:"fc-timegrid-event-harness"+(y?" fc-timegrid-event-harness-inset":""),key:l||m,style:Object.assign(Object.assign({visibility:f?"":"hidden"},h),b)},r.createElement(k,Object.assign({seg:a,isDragging:n,isResizing:o,isDateSelecting:s,isSelected:m===g,isShort:x},i.getSegMeta(a,u,p))))}))}renderHiddenGroups(e,t){let{extraDateSpan:n,dateProfile:o,todayRange:s,nowDate:l,eventSelection:a,eventDrag:c,eventResize:d}=this.props;return r.createElement(r.Fragment,null,e.map(e=>{let m=N(e.span),f=(h=e.entries,g=t,h.map(e=>g[e.index]));var h,g;return r.createElement(b,{key:i.buildIsoString(i.computeEarliestSegStart(f)),hiddenSegs:f,top:m.top,bottom:m.bottom,extraDateSpan:n,dateProfile:o,todayRange:s,nowDate:l,eventSelection:a,eventDrag:c,eventResize:d})}))}renderFillSegs(e,t){let{props:n,context:o}=this,s=D(e,n.date,n.slatCoords,o.options.eventMinHeight).map((o,s)=>{let l=e[s];return r.createElement("div",{key:i.buildEventRangeKey(l.eventRange),className:"fc-timegrid-bg-harness",style:N(o)},"bg-event"===t?r.createElement(i.BgEvent,Object.assign({seg:l},i.getSegMeta(l,n.todayRange,n.nowDate))):i.renderFill(t))});return r.createElement(r.Fragment,null,s)}renderNowIndicator(e){let{slatCoords:t,date:n}=this.props;return t?e.map((e,o)=>r.createElement(i.NowIndicatorContainer,{key:o,elClasses:["fc-timegrid-now-indicator-line"],elStyle:{top:t.computeDateTop(e.start,n)},isAxis:!1,date:n})):null}computeSegHStyle(e){let t,i,{isRtl:r,options:n}=this.context,o=n.slotEventOverlap,s=e.levelCoord,l=e.levelCoord+e.thickness;o&&(l=Math.min(1,s+2*(l-s))),r?(t=1-l,i=s):(t=s,i=1-l);let a={zIndex:e.stackDepth+1,left:100*t+"%",right:100*i+"%"};return o&&!e.stackForward&&(a[r?"marginLeft":"marginRight"]=20),a}}function T(e,{todayRange:t,nowDate:n,eventSelection:o,eventDrag:s,eventResize:l}){let a=(s?s.affectedInstances:null)||(l?l.affectedInstances:null)||{};return r.createElement(r.Fragment,null,e.map(e=>{let s=e.eventRange.instance.instanceId;return r.createElement("div",{key:s,style:{visibility:a[s]?"hidden":""}},r.createElement(k,Object.assign({seg:e,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:s===o,isShort:!1},i.getSegMeta(e,t,n))))}))}function N(e){return e?{top:e.start,bottom:-e.end}:{top:"",bottom:""}}class P extends i.BaseComponent{constructor(){super(...arguments),this.splitFgEventSegs=i.memoize(p),this.splitBgEventSegs=i.memoize(p),this.splitBusinessHourSegs=i.memoize(p),this.splitNowIndicatorSegs=i.memoize(p),this.splitDateSelectionSegs=i.memoize(p),this.splitEventDrag=i.memoize(v),this.splitEventResize=i.memoize(v),this.rootElRef=r.createRef(),this.cellElRefs=new i.RefMap}render(){let{props:e,context:t}=this,n=t.options.nowIndicator&&e.slatCoords&&e.slatCoords.safeComputeTop(e.nowDate),o=e.cells.length,s=this.splitFgEventSegs(e.fgEventSegs,o),l=this.splitBgEventSegs(e.bgEventSegs,o),a=this.splitBusinessHourSegs(e.businessHourSegs,o),c=this.splitNowIndicatorSegs(e.nowIndicatorSegs,o),d=this.splitDateSelectionSegs(e.dateSelectionSegs,o),m=this.splitEventDrag(e.eventDrag,o),f=this.splitEventResize(e.eventResize,o);return r.createElement("div",{className:"fc-timegrid-cols",ref:this.rootElRef},r.createElement("table",{role:"presentation",style:{minWidth:e.tableMinWidth,width:e.clientWidth}},e.tableColGroupNode,r.createElement("tbody",{role:"presentation"},r.createElement("tr",{role:"row"},e.axis&&r.createElement("td",{"aria-hidden":!0,className:"fc-timegrid-col fc-timegrid-axis"},r.createElement("div",{className:"fc-timegrid-col-frame"},r.createElement("div",{className:"fc-timegrid-now-indicator-container"},"number"==typeof n&&r.createElement(i.NowIndicatorContainer,{elClasses:["fc-timegrid-now-indicator-arrow"],elStyle:{top:n},isAxis:!0,date:e.nowDate})))),e.cells.map((t,i)=>r.createElement(M,{key:t.key,elRef:this.cellElRefs.createRef(t.key),dateProfile:e.dateProfile,date:t.date,nowDate:e.nowDate,todayRange:e.todayRange,extraRenderProps:t.extraRenderProps,extraDataAttrs:t.extraDataAttrs,extraClassNames:t.extraClassNames,extraDateSpan:t.extraDateSpan,fgEventSegs:s[i],bgEventSegs:l[i],businessHourSegs:a[i],nowIndicatorSegs:c[i],dateSelectionSegs:d[i],eventDrag:m[i],eventResize:f[i],slatCoords:e.slatCoords,eventSelection:e.eventSelection,forPrint:e.forPrint}))))))}componentDidMount(){this.updateCoords()}componentDidUpdate(){this.updateCoords()}updateCoords(){let{props:e}=this;var t;e.onColCoords&&null!==e.clientWidth&&e.onColCoords(new i.PositionCache(this.rootElRef.current,(t=this.cellElRefs.currentMap,e.cells.map(e=>t[e.key])),!0,!1))}}class z extends i.DateComponent{constructor(){super(...arguments),this.processSlotOptions=i.memoize(H),this.state={slatCoords:null},this.handleRootEl=e=>{e?this.context.registerInteractiveComponent(this,{el:e,isHitComboAllowed:this.props.isHitComboAllowed}):this.context.unregisterInteractiveComponent(this)},this.handleScrollRequest=e=>{let{onScrollTopRequest:t}=this.props,{slatCoords:i}=this.state;if(t&&i){if(e.time){let r=i.computeTimeTop(e.time);r=Math.ceil(r),r&&(r+=1),t(r)}return!0}return!1},this.handleColCoords=e=>{this.colCoords=e},this.handleSlatCoords=e=>{this.setState({slatCoords:e}),this.props.onSlatCoords&&this.props.onSlatCoords(e)}}render(){let{props:e,state:t}=this;return r.createElement("div",{className:"fc-timegrid-body",ref:this.handleRootEl,style:{width:e.clientWidth,minWidth:e.tableMinWidth}},r.createElement(u,{axis:e.axis,dateProfile:e.dateProfile,slatMetas:e.slatMetas,clientWidth:e.clientWidth,minHeight:e.expandRows?e.clientHeight:"",tableMinWidth:e.tableMinWidth,tableColGroupNode:e.axis?e.tableColGroupNode:null,onCoords:this.handleSlatCoords}),r.createElement(P,{cells:e.cells,axis:e.axis,dateProfile:e.dateProfile,businessHourSegs:e.businessHourSegs,bgEventSegs:e.bgEventSegs,fgEventSegs:e.fgEventSegs,dateSelectionSegs:e.dateSelectionSegs,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,todayRange:e.todayRange,nowDate:e.nowDate,nowIndicatorSegs:e.nowIndicatorSegs,clientWidth:e.clientWidth,tableMinWidth:e.tableMinWidth,tableColGroupNode:e.tableColGroupNode,slatCoords:t.slatCoords,onColCoords:this.handleColCoords,forPrint:e.forPrint}))}componentDidMount(){this.scrollResponder=this.context.createScrollResponder(this.handleScrollRequest)}componentDidUpdate(e){this.scrollResponder.update(e.dateProfile!==this.props.dateProfile)}componentWillUnmount(){this.scrollResponder.detach()}queryHit(e,t){let{dateEnv:r,options:n}=this.context,{colCoords:o}=this,{dateProfile:s}=this.props,{slatCoords:l}=this.state,{snapDuration:a,snapsPerSlot:c}=this.processSlotOptions(this.props.slotDuration,n.snapDuration),d=o.leftToIndex(e),m=l.positions.topToIndex(t);if(null!=d&&null!=m){let e=this.props.cells[d],n=l.positions.tops[m],f=l.positions.getHeight(m),h=(t-n)/f,g=m*c+Math.floor(h*c),u=this.props.cells[d].date,p=i.addDurations(s.slotMinTime,i.multiplyDuration(a,g)),v=r.add(u,p),b=r.add(v,a);return{dateProfile:s,dateSpan:Object.assign({range:{start:v,end:b},allDay:!1},e.extraDateSpan),dayEl:o.els[d],rect:{left:o.lefts[d],right:o.rights[d],top:n,bottom:n+f},layer:0}}return null}}function H(e,t){let r=t||e,n=i.wholeDivideDurations(e,r);return null===n&&(r=e,n=1),{snapDuration:r,snapsPerSlot:n}}class W extends i.Slicer{sliceRange(e,t){let r=[];for(let n=0;n<t.length;n+=1){let o=i.intersectRanges(e,t[n]);o&&r.push({start:o.start,end:o.end,isStart:o.start.valueOf()===e.start.valueOf(),isEnd:o.end.valueOf()===e.end.valueOf(),col:n})}return r}}class G extends i.DateComponent{constructor(){super(...arguments),this.buildDayRanges=i.memoize(I),this.slicer=new W,this.timeColsRef=r.createRef()}render(){let{props:e,context:t}=this,{dateProfile:n,dayTableModel:o}=e,{nowIndicator:s,nextDayThreshold:l}=t.options,a=this.buildDayRanges(o,n,t.dateEnv);return r.createElement(i.NowTimer,{unit:s?"minute":"day"},(i,c)=>r.createElement(z,Object.assign({ref:this.timeColsRef},this.slicer.sliceProps(e,n,null,t,a),{forPrint:e.forPrint,axis:e.axis,dateProfile:n,slatMetas:e.slatMetas,slotDuration:e.slotDuration,cells:o.cells[0],tableColGroupNode:e.tableColGroupNode,tableMinWidth:e.tableMinWidth,clientWidth:e.clientWidth,clientHeight:e.clientHeight,expandRows:e.expandRows,nowDate:i,nowIndicatorSegs:s&&this.slicer.sliceNowDate(i,n,l,t,a),todayRange:c,onScrollTopRequest:e.onScrollTopRequest,onSlatCoords:e.onSlatCoords})))}}function I(e,t,i){let r=[];for(let n of e.headerDates)r.push({start:i.add(n,t.slotMinTime),end:i.add(n,t.slotMaxTime)});return r}const F=[{hours:1},{minutes:30},{minutes:15},{seconds:30},{seconds:15}];function O(e,t,r,n,o){let s=new Date(0),l=e,a=i.createDuration(0),c=r||function(e){let t,r,n;for(t=F.length-1;t>=0;t-=1)if(r=i.createDuration(F[t]),n=i.wholeDivideDurations(r,e),null!==n&&n>1)return r;return e}(n),d=[];for(;i.asRoughMs(l)<i.asRoughMs(t);){let e=o.add(s,l),t=null!==i.wholeDivideDurations(a,c);d.push({date:e,time:l,key:e.toISOString(),isoTimeStr:i.formatIsoTimeString(e),isLabeled:t}),l=i.addDurations(l,n),a=i.addDurations(a,n)}return d}class L extends m{constructor(){super(...arguments),this.buildTimeColsModel=i.memoize(A),this.buildSlatMetas=i.memoize(O)}render(){let{options:e,dateEnv:t,dateProfileGenerator:o}=this.context,{props:s}=this,{dateProfile:l}=s,a=this.buildTimeColsModel(l,o),c=this.allDaySplitter.splitProps(s),d=this.buildSlatMetas(l.slotMinTime,l.slotMaxTime,e.slotLabelInterval,e.slotDuration,t),{dayMinWidth:m}=e,f=!m,h=m,g=e.dayHeaders&&r.createElement(i.DayHeader,{dates:a.headerDates,dateProfile:l,datesRepDistinctDays:!0,renderIntro:f?this.renderHeadAxis:null}),u=!1!==e.allDaySlot&&(t=>r.createElement(n.DayTable,Object.assign({},c.allDay,{dateProfile:l,dayTableModel:a,nextDayThreshold:e.nextDayThreshold,tableMinWidth:t.tableMinWidth,colGroupNode:t.tableColGroupNode,renderRowIntro:f?this.renderTableRowAxis:null,showWeekNumbers:!1,expandRows:!1,headerAlignElRef:this.headerElRef,clientWidth:t.clientWidth,clientHeight:t.clientHeight,forPrint:s.forPrint},this.getAllDayMaxEventProps()))),p=t=>r.createElement(G,Object.assign({},c.timed,{dayTableModel:a,dateProfile:l,axis:f,slotDuration:e.slotDuration,slatMetas:d,forPrint:s.forPrint,tableColGroupNode:t.tableColGroupNode,tableMinWidth:t.tableMinWidth,clientWidth:t.clientWidth,clientHeight:t.clientHeight,onSlatCoords:this.handleSlatCoords,expandRows:t.expandRows,onScrollTopRequest:this.handleScrollTopRequest}));return h?this.renderHScrollLayout(g,u,p,a.colCnt,m,d,this.state.slatCoords):this.renderSimpleLayout(g,u,p)}}function A(e,t){let r=new i.DaySeriesModel(e.renderRange,t);return new i.DayTableModel(r,!1)}const j={allDaySlot:Boolean};i.injectStyles('.fc-v-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-v-event .fc-event-main{color:var(--fc-event-text-color);height:100%}.fc-v-event .fc-event-main-frame{display:flex;flex-direction:column;height:100%}.fc-v-event .fc-event-time{flex-grow:0;flex-shrink:0;max-height:100%;overflow:hidden}.fc-v-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-height:0}.fc-v-event .fc-event-title{bottom:0;max-height:100%;overflow:hidden;top:0}.fc-v-event:not(.fc-event-start){border-top-left-radius:0;border-top-right-radius:0;border-top-width:0}.fc-v-event:not(.fc-event-end){border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-width:0}.fc-v-event.fc-event-selected:before{left:-10px;right:-10px}.fc-v-event .fc-event-resizer-start{cursor:n-resize}.fc-v-event .fc-event-resizer-end{cursor:s-resize}.fc-v-event:not(.fc-event-selected) .fc-event-resizer{height:var(--fc-event-resizer-thickness);left:0;right:0}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-start{top:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer{left:50%;margin-left:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-start{top:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc .fc-timegrid .fc-daygrid-body{z-index:2}.fc .fc-timegrid-divider{padding:0 0 2px}.fc .fc-timegrid-body{min-height:100%;position:relative;z-index:1}.fc .fc-timegrid-axis-chunk{position:relative}.fc .fc-timegrid-axis-chunk>table,.fc .fc-timegrid-slots{position:relative;z-index:1}.fc .fc-timegrid-slot{border-bottom:0;height:1.5em}.fc .fc-timegrid-slot:empty:before{content:"\\00a0"}.fc .fc-timegrid-slot-minor{border-top-style:dotted}.fc .fc-timegrid-slot-label-cushion{display:inline-block;white-space:nowrap}.fc .fc-timegrid-slot-label{vertical-align:middle}.fc .fc-timegrid-axis-cushion,.fc .fc-timegrid-slot-label-cushion{padding:0 4px}.fc .fc-timegrid-axis-frame-liquid{height:100%}.fc .fc-timegrid-axis-frame{align-items:center;display:flex;justify-content:flex-end;overflow:hidden}.fc .fc-timegrid-axis-cushion{flex-shrink:0;max-width:60px}.fc-direction-ltr .fc-timegrid-slot-label-frame{text-align:right}.fc-direction-rtl .fc-timegrid-slot-label-frame{text-align:left}.fc-liquid-hack .fc-timegrid-axis-frame-liquid{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-timegrid-col-frame{min-height:100%;position:relative}.fc-media-screen.fc-liquid-hack .fc-timegrid-col-frame{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols{bottom:0;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols>table{height:100%}.fc-media-screen .fc-timegrid-col-bg,.fc-media-screen .fc-timegrid-col-events,.fc-media-screen .fc-timegrid-now-indicator-container{left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col-bg{z-index:2}.fc .fc-timegrid-col-bg .fc-non-business{z-index:1}.fc .fc-timegrid-col-bg .fc-bg-event{z-index:2}.fc .fc-timegrid-col-bg .fc-highlight{z-index:3}.fc .fc-timegrid-bg-harness{left:0;position:absolute;right:0}.fc .fc-timegrid-col-events{z-index:3}.fc .fc-timegrid-now-indicator-container{bottom:0;overflow:hidden}.fc-direction-ltr .fc-timegrid-col-events{margin:0 2.5% 0 2px}.fc-direction-rtl .fc-timegrid-col-events{margin:0 2px 0 2.5%}.fc-timegrid-event-harness{position:absolute}.fc-timegrid-event-harness>.fc-timegrid-event{bottom:0;left:0;position:absolute;right:0;top:0}.fc-timegrid-event-harness-inset .fc-timegrid-event,.fc-timegrid-event.fc-event-mirror,.fc-timegrid-more-link{box-shadow:0 0 0 1px var(--fc-page-bg-color)}.fc-timegrid-event,.fc-timegrid-more-link{border-radius:3px;font-size:var(--fc-small-font-size)}.fc-timegrid-event{margin-bottom:1px}.fc-timegrid-event .fc-event-main{padding:1px 1px 0}.fc-timegrid-event .fc-event-time{font-size:var(--fc-small-font-size);margin-bottom:1px;white-space:nowrap}.fc-timegrid-event-short .fc-event-main-frame{flex-direction:row;overflow:hidden}.fc-timegrid-event-short .fc-event-time:after{content:"\\00a0-\\00a0"}.fc-timegrid-event-short .fc-event-title{font-size:var(--fc-small-font-size)}.fc-timegrid-more-link{background:var(--fc-more-link-bg-color);color:var(--fc-more-link-text-color);cursor:pointer;margin-bottom:1px;position:absolute;z-index:9999}.fc-timegrid-more-link-inner{padding:3px 2px;top:0}.fc-direction-ltr .fc-timegrid-more-link{right:0}.fc-direction-rtl .fc-timegrid-more-link{left:0}.fc .fc-timegrid-now-indicator-arrow,.fc .fc-timegrid-now-indicator-line{pointer-events:none}.fc .fc-timegrid-now-indicator-line{border-color:var(--fc-now-indicator-color);border-style:solid;border-width:1px 0 0;left:0;position:absolute;right:0;z-index:4}.fc .fc-timegrid-now-indicator-arrow{border-color:var(--fc-now-indicator-color);border-style:solid;margin-top:-5px;position:absolute;z-index:4}.fc-direction-ltr .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 0 5px 6px;left:0}.fc-direction-rtl .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 6px 5px 0;right:0}');var B=t.createPlugin({name:"@fullcalendar/timegrid",initialView:"timeGridWeek",optionRefiners:j,views:{timeGrid:{component:L,usesMinMaxTime:!0,allDaySlot:!0,slotDuration:"00:30:00",slotEventOverlap:!0},timeGridDay:{type:"timeGrid",duration:{days:1}},timeGridWeek:{type:"timeGrid",duration:{weeks:1}}}}),q={__proto__:null,TimeColsView:m,DayTimeColsView:L,buildTimeColsModel:A,DayTimeCols:G,buildDayRanges:I,DayTimeColsSlicer:W,TimeCols:z,buildSlatMetas:O,TimeColsSlatsCoords:h};return t.globalPlugins.push(B),e.Internal=q,e.default=B,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar,FullCalendar.Internal,FullCalendar.Preact,FullCalendar.DayGrid.Internal);