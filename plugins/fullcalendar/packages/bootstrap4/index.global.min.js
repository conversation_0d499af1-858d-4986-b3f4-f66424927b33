/*!
FullCalendar Bootstrap 4 Plugin v6.1.19
Docs & License: https://fullcalendar.io/docs/bootstrap4
(c) 2024 Adam <PERSON>
*/
FullCalendar.Bootstrap=function(e,t,o){"use strict";class r extends o.Theme{}r.prototype.classes={root:"fc-theme-bootstrap",table:"table-bordered",tableCellShaded:"table-active",buttonGroup:"btn-group",button:"btn btn-primary",buttonActive:"active",popover:"popover",popoverHeader:"popover-header",popoverContent:"popover-body"},r.prototype.baseIconClass="fa",r.prototype.iconClasses={close:"fa-times",prev:"fa-chevron-left",next:"fa-chevron-right",prevYear:"fa-angle-double-left",nextYear:"fa-angle-double-right"},r.prototype.rtlIconClasses={prev:"fa-chevron-right",next:"fa-chevron-left",prevYear:"fa-angle-double-right",nextYear:"fa-angle-double-left"},r.prototype.iconOverrideOption="bootstrapFontAwesome",r.prototype.iconOverrideCustomButtonOption="bootstrapFontAwesome",r.prototype.iconOverridePrefix="fa-";o.injectStyles(".fc-theme-bootstrap a:not([href]){color:inherit}.fc-theme-bootstrap .fc-more-link:hover{text-decoration:none}");var a=t.createPlugin({name:"@fullcalendar/bootstrap",themeClasses:{bootstrap:r}}),n={__proto__:null,BootstrapTheme:r};return t.globalPlugins.push(a),e.Internal=n,e.default=a,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar,FullCalendar.Internal);