const interfaceTranslations = {
  selectedCountryAriaLabel: "Vybraná země",
  noCountrySelected: "<PERSON>ení vy<PERSON>r<PERSON> ž<PERSON>dn<PERSON> země",
  countryListAriaLabel: "Seznam zemí",
  searchPlaceholder: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  zeroSearchResults: "Nebyly nalezeny žádné výsledky",
  oneSearchResult: "1 výsledek nalezen",
  multipleSearchResults: "${count} výsledků nalezeno",
  // additional countries (not supported by country-list library)
  ac: "Ascension",
  xk: "Kosovo"
};
export default interfaceTranslations;
