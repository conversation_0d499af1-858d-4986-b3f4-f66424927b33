const interfaceTranslations = {
  selectedCountryAriaLabel: "Paese selezionato",
  noCountrySelected: "Nessun paese selezionato",
  countryListAriaLabel: "Elenco dei paesi",
  searchPlaceholder: "Ricerca",
  zeroSearchResults: "Nessun risultato trovato",
  oneSearchResult: "1 risultato trovato",
  multipleSearchResults: "${count} risultati trovati",
  // additional countries (not supported by country-list library)
  ac: "Isola di Ascensione",
  xk: "Kosovo"
};
export default interfaceTranslations;
