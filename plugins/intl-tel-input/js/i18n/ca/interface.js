const interfaceTranslations = {
  selectedCountryAriaLabel: "<PERSON><PERSON> seleccionat",
  noCountrySelected: "No s'ha seleccionat cap país",
  countryListAriaLabel: "Llista de països",
  searchPlaceholder: "<PERSON><PERSON>",
  zeroSearchResults: "Sense resultats",
  oneSearchResult: "1 resultat trobat",
  multipleSearchResults: "${count} resultats trobats",
  // additional countries (not supported by country-list library)
  ac: "Illa de l'Ascensió",
  xk: "Kosovo"
};
export default interfaceTranslations;
