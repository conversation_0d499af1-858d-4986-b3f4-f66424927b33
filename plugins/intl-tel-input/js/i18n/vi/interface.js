const interfaceTranslations = {
  selectedCountryAriaLabel: "Quốc gia đã chọn",
  noCountrySelected: "<PERSON>hông có quốc gia nào được chọn",
  countryListAriaLabel: "<PERSON><PERSON> sách các quốc gia",
  searchPlaceholder: "Khám xét",
  zeroSearchResults: "Không tìm thấy kết quả nào",
  oneSearchResult: "Đã tìm thấy 1 kết quả",
  multipleSearchResults: "Đã tìm thấy ${count} kết quả",
  // additional countries (not supported by country-list library)
  ac: "Đảo Ascension",
  xk: "Kosovo"
};
export default interfaceTranslations;
