const interfaceTranslations = {
  selectedCountryAriaLabel: "Negara yang dipilih",
  noCountrySelected: "Tidak ada negara yang dipilih",
  countryListAriaLabel: "Daftar negara",
  searchPlaceholder: "<PERSON><PERSON><PERSON>",
  zeroSearchResults: "Tidak ada hasil yang ditemukan",
  oneSearchResult: "1 hasil ditemukan",
  multipleSearchResults: "${count} hasil ditemukan",
  // additional countries (not supported by country-list library)
  ac: "Pulau Kenaikan",
  xk: "Kosovo"
};
export default interfaceTranslations;
