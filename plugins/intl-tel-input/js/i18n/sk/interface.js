const interfaceTranslations = {
  selectedCountryAriaLabel: "Vybraná krajina",
  noCountrySelected: "Nie je vybratá žiadna krajina",
  countryListAriaLabel: "Zoznam krajín",
  searchPlaceholder: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  zeroSearchResults: "Neboli nájdené žiadne výsledky",
  oneSearchResult: "1 nájdený výsledok",
  multipleSearchResults: "${count} nájdených výsledkov",
  // additional countries (not supported by country-list library)
  ac: "Ascension",
  xk: "Kosovo"
};
export default interfaceTranslations;
